<?php
#region region DOCS
/** @var PartidoPorRevisar[] $partidosporrevisar */
/** @var int $n_fila */
/** @var PartidoPorRevisar[] $partidos_porrevisar_bytorneo */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title>My Dash | Partidos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<link id="region_CSS_date" href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #region region CSS datatable ?>
	<link href="<?php echo RUTA ?>resources/assets/plugins/datatables.net-bs5/css/dataTables.bootstrap5.min.css" rel="stylesheet"/>
	<link href="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive-bs5/css/responsive.bootstrap5.min.css" rel="stylesheet"/>
	<?php #endregion CSS datatable ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->
<?php #region region DESCRIPTION ?>

<?php #endregion DESCRIPTION ?>
<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region FORM ?>
		<form action="lpartidosporrevisar" method="POST" enctype="multipart/form-data">
			<input type="hidden" id="selidpartidoporrevisar" name="selidpartidoporrevisar">
			<input type="hidden" id="selnombrepais" name="selnombrepais">
			<input type="hidden" id="rowfocus" name="rowfocus">
			<input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">
			
			<!-- BEGIN SUBMIT editpartidoporrevisar -->
			<div class="col" style="display: none">
				<button type="submit" id="sub_editpartidoporrevisar" name="sub_editpartidoporrevisar" class="region_SUBMIT_editpartidoporrevisar btn btn-xs btn-success w-100">
					sub_editpartidoporrevisar
				</button>
			</div>
			<!-- END SUBMIT editpartidoporrevisar -->
			<!-- BEGIN SUBMIT addpartido -->
			<div class="col" style="display: none">
				<button type="submit" id="sub_addpartido" name="sub_addpartido" class="region_SUBMIT_addpartido btn btn-xs btn-success w-100">
					sub_addpartido
				</button>
			</div>
			<!-- END SUBMIT addpartido -->
			<?php #region region SUBMIT sub_del_all_same_torneo ?>
			<div class="col" style="display: none">
				<button type="submit" id="sub_del_all_same_torneo" name="sub_del_all_same_torneo" class="btn btn-success w-100">
					sub_del_all_same_torneo
				</button>
			</div>
			<?php #endregion sub_del_all_same_torneo ?>
			

			<!-- BEGIN ROW - File Upload -->
			<div class="row mt-3">
				<div class="col-md-8 col-xs-12 form-group">
					<div class="custom-file-input no-border-radious" data-placeholder="Subir archivo: partidos por revisar">
						<input type="file" class="form-control form-control-fh no-border-radious" id="archivocsv" name="archivocsv"/>
					</div>
				</div>
				<div class="col-md-4 col-xs-12 form-group">
					<button type="submit" id="sub_subir" name="sub_subir" class="btn btn-lg btn-success w-100">
						Subir archivo partidos por revisar
					</button>
				</div>
			</div>
			<!-- END ROW - File Upload -->
			
			<hr>

			<!-- BEGIN ROW - API Integration -->
			<div class="row mt-3">
				<div class="col-md-8 col-xs-12 form-group">
					<div class="input-group">
						<input type="text" class="form-control form-control-fh fs-12px no-border-radious datepicker" id="fecha_api" name="fecha_api" placeholder="yyyy-mm-dd" autocomplete="off"/>
						<span class="input-group-text no-border-radious mouse-pointer" onclick="region_JS_sub_clearfecha_api();">
							<i class="fa fa-xmark fa-md cursor-pointer"></i>
						</span>
					</div>
				</div>
				<div class="col-md-4 col-xs-12 form-group">
					<button type="submit" id="sub_cargar_api" name="sub_cargar_api" class="btn btn-sm btn-primary w-100">
						Cargar partidos via API
					</button>
				</div>
			</div>
			<!-- END ROW - API Integration -->
			<?php #region region NAVTAB partidos ?>
			<?php #region region NAVTAB HEAD ?>
			<ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
				<li class="nav-item" onclick="tabselect(1)">
					<a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
						Partidos
						<span id="numero_partidos" class="badge bg-primary rounded-0 fs-10px ms-1"></span>
					</a>
				</li>
				<li class="nav-item" onclick="tabselect(2)">
					<a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">
						Torneos
						<span id="numero_torneos" class="badge bg-primary rounded-0 fs-10px ms-1"></span>
					</a>
				</li>
			</ul>
			<?php #endregion NAVTAB HEAD ?>
			<div class="tab-content panel rounded-0 rounded-bottom">
				<?php #region region TAB partidos por revisar ?>
				<div class="tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
					<?php #region region BUTTON convertir todos ?>
					<div class="row mb-3">
						<div class="col-12">
							<button type="button" id="btn_convertir_todos" class="btn btn-success btn-sm w-100" onclick="convertirTodosLosPartidos()">
								<i class="fas fa-exchange-alt me-1"></i>Convertir Todos los Partidos
							</button>
						</div>
					</div>
					<?php #endregion BUTTON convertir todos ?>
					<?php #region region TABLE partidos por revisar ?>
					<table id="table_partidos" class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="w-100px"></th>
							<th class="text-center">Fecha</th>
							<th class="text-center">Pais</th>
							<th class="text-center">Matchup</th>
							<th class="text-center">Torneo<br>creado</th>
							<th class="text-center">Info<br>cargada</th>
						</tr>
						</thead>
						<tbody class="fs-10px">
						
						</tbody>
					</table>
					<?php #endregion TABLE partidos por revisar ?>
				</div>
				<?php #endregion TAB partidos por revisar ?>
				<?php #region region TAB torneos ?>
				<div class="tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
					<?php #region region TABLE torneos ?>
					<table id="table_torneos" class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="w-50px"></th>
							<th class="text-center">Torneo</th>
							<th class="text-center"># partidos</th>
							<th class="text-center">Torneo<br>creado</th>
							<th class="text-center">Info<br>cargada</th>
						</tr>
						</thead>
						<tbody class="fs-12px">
						
						</tbody>
					</table>
					<?php #endregion table torneos ?>
				</div>
				<?php #endregion TAB torneos ?>
			</div>
			<?php #endregion NAVTAB partidos ?>
			<?php #region region MODAL delpartidosporrevisar ?>
			<div class="modal fade" id="delpartidosporrevisar">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">Eliminar todos los partidos</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body">
							<p>Esta seguro que desea eliminar todos los partidos?</p>
						</div>
						<div class="modal-footer">
							<a href="#" class="btn btn-xs btn-default no-border-radious" data-bs-dismiss="modal">
								Cancelar
							</a>
							<button type="submit" id="sub_delpartidosporrevisar" name="sub_delpartidosporrevisar" class="btn btn-xs btn-danger no-border-radious">
								Eliminar
							</button>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL delpartidosporrevisar ?>
			<?php #region region MODAL mdl_delpartidoporrevisar ?>
			<div class="region_MODAL_mdl_delpartidoporrevisar modal fade" id="mdl_delpartidoporrevisar">
				<div class="modal-dialog modal-dialog-centered">
					<div class="modal-content">
						<input type="hidden" id="mdl_delpartidoporrevisar_idpartidoporrevisar" name="mdl_delpartidoporrevisar_idpartidoporrevisar">
						
						<div class="modal-header">
							<h4 class="modal-title">Eliminar partido</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body">
							<p>Esta seguro que desea eliminar este partido?</p>
						</div>
						<div class="modal-footer">
							<a href="#" class="btn btn-xs btn-default no-border-radious" data-bs-dismiss="modal">
								Cancelar
							</a>
							<button type="submit" id="sub_delpartidoporrevisar" name="sub_delpartidoporrevisar" class="btn btn-xs btn-danger no-border-radious">
								Eliminar
							</button>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL mdl_delpartidoporrevisar ?>
			<?php #region region MODAL mdl_eliminar_partidosporrevisar_hasta ?>
			<div class="modal fade" id="mdl_eliminar_partidosporrevisar_hasta">
				<div class="modal-dialog modal-lg modal-dialog-centered">
					<div class="modal-content">
						<div class="modal-header">
							<h4 class="modal-title">Eliminar partidos</h4>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
						</div>
						<div class="modal-body">
							<!-- BEGIN text -->
							<div class="col-md-12 col-xs-12">
								<div class="input-group">
							        <span class="input-group-text no-border-radious bg-gray fs-11px w-100px">
							            Hora militar:
							        </span>
									<input type="text" name="eliminar_partidosporrevisar_hasta_hora" id="eliminar_partidosporrevisar_hasta_hora" class="form-control form-control-fh fs-12px no-border-radious "/>
								</div>
							</div>
							<!-- END text -->
						</div>
						<div class="modal-footer">
							<a href="#" class="btn btn-white" data-bs-dismiss="modal">
								Cancelar
							</a>
							<button type="submit" id="sub_eliminar_partidosporrevisar_hasta" name="sub_eliminar_partidosporrevisar_hasta" class="btn btn-danger no-border-radious">
								Eliminar
							</button>
						</div>
					</div>
				</div>
			</div>
			<?php #endregion MODAL mdl_eliminar_partidosporrevisar_hasta ?>
		</form>
		<?php #endregion FORM ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<!-- Datepicker scripts to match lpartidos implementation -->
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>

<?php #region region JS click sub_editpartidoporrevisar ?>
<script type="text/javascript">
    function region_JS_editpartidoporrevisar($idbudget) {
        const selidpartidoporrevisar = document.getElementById('selidpartidoporrevisar');
        selidpartidoporrevisar.value = $idbudget;
        
        document.getElementById('sub_editpartidoporrevisar').click();
    }
</script>
<?php #endregion JS click sub_editpartidoporrevisar ?>
<?php #region region JS click sub_addpartido ?>
<script type="text/javascript">
    function region_JS_addpartido($idbudget) {
        const selidpartidoporrevisar = document.getElementById('selidpartidoporrevisar');
        selidpartidoporrevisar.value = $idbudget;
        
        document.getElementById('sub_addpartido').click();
    }
</script>
<?php #endregion JS click sub_addpartido ?>
<?php #region region JS MODAL mdl_delpartidoporrevisar ?>
<script type="text/javascript">
    $('#mdl_delpartidoporrevisar').on('shown.bs.modal', function (event) {
        const button                        = $(event.relatedTarget);
        const recipient_idpartidoporrevisar = button.data('idpartidoporrevisar');
        
        const mdl_delpartidoporrevisar_idpartidoporrevisar = document.getElementById('mdl_delpartidoporrevisar_idpartidoporrevisar');
        
        mdl_delpartidoporrevisar_idpartidoporrevisar.value = recipient_idpartidoporrevisar;
    })
</script>
<?php #endregion JS MODAL mdl_delpartidoporrevisar ?>
<?php #region region JS tabselect ?>
<script type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<?php #endregion JS tabselect ?>
<?php #region region JS del_all_same_torneo ?>
<script type="text/javascript">
    function del_all_same_torneo($pais) {
        const selnombrepais = document.getElementById('selnombrepais');
        selnombrepais.value = $pais;
        
        document.getElementById('sub_del_all_same_torneo').click();
    }
</script>
<?php #endregion js del_all_same_torneo ?>
<?php #region region JS custom inputfile archivocsv ?>
<script>
    const fileInput       = document.getElementById('archivocsv');
    const customFileInput = fileInput.parentElement;
    
    fileInput.addEventListener('change', function () {
        if (this.files.length > 0) {
            customFileInput.setAttribute('data-placeholder', this.files[0].name);
        } else {
            customFileInput.setAttribute('data-placeholder', 'Subir archivo: partidos por revisar');
        }
    });
</script>
<?php #endregion JS custome inputfile archivocsv ?>
<?php #region region JS datatable ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>
<?php #endregion JS datatable ?>
<?php #region region JS page load ?>
<script>
    $(document).ready(function () {
        updateTableTorneos();
        updateTablePartidos();
        
        // Initialize table_partidos only when its tab is shown
        $('a[href="#default-tab-1"]').on('shown.bs.tab', function () {
            updateTablePartidos();
        });
        
        // Initialize table_torneos only when tab is shown
        $('a[href="#default-tab-2"]').on('shown.bs.tab', function () {
            updateTableTorneos();
        });
        
        // Trigger the `shown.bs.tab` event for the active tab on page load
        if ($('a[href="#default-tab-1"]').hasClass('active')) {
            $('a[href="#default-tab-1"]').trigger('shown.bs.tab');
        } else if ($('a[href="#default-tab-2"]').hasClass('active')) {
            $('a[href="#default-tab-2"]').trigger('shown.bs.tab');
        }
    });
</script>
<?php #endregion JS page load ?>
<?php #region region JS AJAX get_list_torneos ?>
<script>
    function updateTableTorneos() {
        // Send the data using POST
        $.ajax({
            url    : 'fetch_lpartidosporrevisar_por_torneo',
            method : 'POST',
            success: function (response) {
                const table_torneos       = $('#table_torneos');
                const span_numero_torneos = $('#numero_torneos');
                
                // Destroy the existing DataTable if it's already initialized
                if ($.fn.DataTable.isDataTable(table_torneos)) {
                    table_torneos.DataTable().destroy();
                }
                
                // Replace the tbody content with the new data
                let table_torneos_tbody = table_torneos.find('tbody');
                table_torneos_tbody.html(response);
                
                // Count the rows in the updated <tbody>
                span_numero_torneos.text(table_torneos_tbody.find('tr').length);
                
                table_torneos.DataTable({
                    responsive : false,
                    info       : false,
                    paging     : false,
                    ordering   : false,
                    fixedHeader: true,
                    searching  : false,
                });
                
                addClickEventToEyeIcons();
            },
            error  : function (xhr, status, error) {
                console.error('Error fetching data:', error);
            }
        });
    }
</script>
<?php #endregion JS AJAX get_list_torneos ?>
<?php #region region JS AJAX get_list_partidos ?>
<script>
    function updateTablePartidos() {
        // Send the data using POST
        $.ajax({
            url    : 'fetch_lpartidosporrevisar',
            method : 'POST',
            success: function (response) {
                const table_partidos       = $('#table_partidos');
                const span_numero_partidos = $('#numero_partidos');
                
                // Destroy the existing DataTable if it's already initialized
                if ($.fn.DataTable.isDataTable(table_partidos)) {
                    table_partidos.DataTable().destroy();
                }
                
                // Replace the tbody content with the new data
                let table_partidos_tbody = table_partidos.find('tbody');
                table_partidos_tbody.html(response);
                
                // Count the rows in the updated <tbody>
                span_numero_partidos.text(table_partidos_tbody.find('tr').length);
                
                table_partidos.DataTable({
                    responsive : false,
                    info       : false,
                    paging     : false,
                    ordering   : false,
                    fixedHeader: true,
                    searching  : false,
                });
            },
            error  : function (xhr, status, error) {
                console.error('Error fetching data:', error);
            }
        });
    }
</script>
<?php #endregion JS AJAX get_list_partidos ?>
<?php #region region JS AJAX agregar evento ocultar pais ?>
<script>
    function addClickEventToEyeIcons() {
        // Add click event listener to all elements with the class "eye-icon"
        document.querySelectorAll(".eye-icon").forEach(function (icon) {
            icon.addEventListener("click", function (event) {
                // Prevent default behavior if needed
                event.preventDefault();
                
                // Get the data-id_partido_porrevisar value from the closest <tr> parent
                let trElement               = event.target.closest("tr");
                let partido_porrevisar_pais = trElement.getAttribute("data-partido_porrevisar_pais");
                
                // Create FormData object
                const formData = new FormData();
                formData.append("ajax", "1");
                formData.append("ajax_ocultar_pais", "1");
                formData.append("partido_porrevisar_pais", partido_porrevisar_pais);
                
                // Send AJAX request
                fetch('lpartidosporrevisar', {
                    method: "POST",
                    body  : formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateTableTorneos();
                            updateTablePartidos();
                            
                        } else {
                            console.error("Error:", data.message);
                        }
                    })
                    .catch(error => console.error("Request failed:", error));
            });
        });
    }
</script>
<?php #endregion JS AJAX agregar evento ocultar ?>
<?php #region region JS AJAX agregar evento eliminar partidos del pais ?>
<script>
    function addClickEventToTrashIcons() {
        // Add click event listener to all elements with the class "eye-icon"
        document.querySelectorAll(".trash-icon").forEach(function (icon) {
            icon.addEventListener("click", function (event) {
                // Prevent default behavior if needed
                event.preventDefault();
                
                // Get the data-id_partido_porrevisar value from the closest <tr> parent
                let trElement               = event.target.closest("tr");
                let partido_porrevisar_pais = trElement.getAttribute("data-partido_porrevisar_pais");
                
                // Create FormData object
                const formData = new FormData();
                formData.append("ajax", "1");
                formData.append("ajax_eliminar_pais", "1");
                formData.append("partido_porrevisar_pais", partido_porrevisar_pais);
                
                // Send AJAX request
                fetch('lpartidosporrevisar', {
                    method: "POST",
                    body  : formData
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            updateTableTorneos();
                            updateTablePartidos();
                            
                        } else {
                            console.error("Error:", data.message);
                        }
                    })
                    .catch(error => console.error("Request failed:", error));
            });
        });
    }
</script>
<?php #endregion JS AJAX agregar evento eliminar partidos del pais ?>
<?php #region region JS focus text input when modal shown ?>
<script>
    focusTextWhenModalShown('#mdl_eliminar_partidosporrevisar_hasta', '#eliminar_partidosporrevisar_hasta_hora');
</script>
<?php #endregion JS focus text input when modal shown ?>
<?php #region region JS datepicker API ?>
<script>
    $(document).ready(function () {
        // Initialize the Bootstrap datepicker (do not auto-submit on change)
        $('#fecha_api').datepicker({
            format        : "yyyy-mm-dd",
            todayHighlight: true,
            autoclose     : true
        });
        // Prevent Enter key from submitting the form when focusing the date field
        $('#fecha_api').on('keydown', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                return false;
            }
        });
    });
</script>
<?php #endregion JS datepicker API ?>

<?php #region region JS clear fecha_api ?>
<script type="text/javascript">
    function region_JS_sub_clearfecha_api() {
        document.getElementById('fecha_api').value = '';
    }
</script>
<?php #endregion JS clear fecha_api ?>

<?php #region region JS convertir todos los partidos ?>
<script type="text/javascript">
function convertirTodosLosPartidos() {
    // Show SweetAlert confirmation dialog
    swal({
        title: '¿Confirmar conversión?',
        text: '¿Está seguro que desea convertir todos los partidos por revisar a partidos? Esta acción no se puede deshacer.',
        icon: 'warning',
        buttons: {
            cancel: {
                text: 'Cancelar',
                value: null,
                visible: true,
                className: 'btn btn-default',
                closeModal: true
            },
            confirm: {
                text: 'Sí, convertir',
                value: true,
                visible: true,
                className: 'btn btn-danger',
                closeModal: true
            }
        },
        dangerMode: true
    }).then((willConvert) => {
        if (willConvert) {
            // Show loading indicator
            const loadingHtml = '<div class="d-flex justify-content-center align-items-center" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999;"><div class="text-center text-white"><div class="spinner-border mb-3" role="status"></div><h5>Convirtiendo partidos...</h5><p>Por favor espere, esto puede tomar varios minutos.</p></div></div>';
            $('body').append(loadingHtml);

            // Disable all buttons to prevent double-clicks
            $('button').prop('disabled', true);

            // Make AJAX call to convert all matches
            $.ajax({
                url: 'lpartidosporrevisar',
                method: 'POST',
                dataType: 'json',
                data: {
                    sub_convertir_todos: 1,
                    ajax: 1
                },
                success: function(response) {
                    // Remove loading indicator
                    $('.d-flex.justify-content-center').remove();
                    $('button').prop('disabled', false);

                    if (response.success) {
                        swal({
                            title: '¡Éxito!',
                            text: response.message,
                            icon: 'success',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn btn-primary',
                                    closeModal: true
                                }
                            }
                        }).then(() => {
                            // Update tables to reflect changes
                            updateTableTorneos();
                            updateTablePartidos();
                        });
                    } else {
                        swal({
                            title: 'Error',
                            text: response.message,
                            icon: 'error',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn btn-primary',
                                    closeModal: true
                                }
                            }
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // Remove loading indicator
                    $('.d-flex.justify-content-center').remove();
                    $('button').prop('disabled', false);

                    console.error('AJAX Error:', error);
                    swal({
                        title: 'Error',
                        text: 'Error al procesar la solicitud. Por favor intente nuevamente.',
                        icon: 'error',
                        buttons: {
                            confirm: {
                                text: 'Ok',
                                value: true,
                                visible: true,
                                className: 'btn btn-primary',
                                closeModal: true
                            }
                        }
                    });
                }
            });
        }
    });
}
</script>
<?php #endregion JS convertir todos los partidos ?>

<?php #region region JS cargar API con loading ?>
<script type="text/javascript">
$(document).ready(function() {
    // Intercept form submission for API loading button
    $('#sub_cargar_api').click(function(e) {
        e.preventDefault(); // Prevent form submission

        // Validate that fecha_api has a value
        const fechaApi = $('#fecha_api').val();
        if (!fechaApi) {
            alert('Debe seleccionar una fecha para cargar partidos via API.');
            return false;
        }

        // Start the API loading process with progress tracking
        startApiLoadingWithProgress(fechaApi);

        return false;
    });
});

function startApiLoadingWithProgress(fechaApi) {
    // Show loading indicator with progress
    const loadingHtml = `
        <div id="api-loading-overlay" class="d-flex justify-content-center align-items-center" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999;">
            <div class="text-center text-white">
                <div class="spinner-border mb-3" role="status"></div>
                <h5>Cargando partidos via API...</h5>
                <div id="progress-info" class="mb-2">
                    <span id="progress-text">Iniciando proceso...</span>
                </div>
                <div class="progress" style="width: 300px; height: 20px;">
                    <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                        <span id="progress-percentage">0%</span>
                    </div>
                </div>
                <p class="mt-2">Por favor espere, esto puede tomar varios minutos.</p>
            </div>
        </div>
    `;
    $('body').append(loadingHtml);

    // Disable all buttons to prevent double-clicks
    $('button').prop('disabled', true);

    // Start the batch processing
    processApiBatch(fechaApi, 0, 0);
}

function processApiBatch(fechaApi, currentBatch, totalProcessed) {
    $.ajax({
        url: 'lpartidosporrevisar',
        method: 'POST',
        dataType: 'json',
        data: {
            ajax: 1,
            ajax_cargar_api_batch: 1,
            fecha_api: fechaApi,
            batch_number: currentBatch,
            tabselected: $('#tabselected').val() || 1
        },
        success: function(response) {
            if (response.success) {
                // Update progress display
                const processed = response.processed || 0;
                const total = response.total || 0;
                const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

                $('#progress-text').text(`${processed} de ${total} partidos procesados`);
                $('#progress-bar').css('width', percentage + '%').attr('aria-valuenow', percentage);
                $('#progress-percentage').text(percentage + '%');

                if (response.completed) {
                    // Process completed successfully
                    setTimeout(function() {
                        $('#api-loading-overlay').remove();
                        $('button').prop('disabled', false);

                        // TEMPORARY DEBUG: Prepare debugging information for display
                        let debugHtml = '';
                        if (response.debug_info) {
                            const debug = response.debug_info;
                            debugHtml = `
                                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; text-align: left;">
                                    <h6 style="color: #6c757d; margin-bottom: 10px;">🔍 INFORMACIÓN DE DEBUG (TEMPORAL):</h6>
                                    <div style="font-size: 12px; color: #495057;">
                                        <strong>API Response:</strong><br>
                                        • Total matches from API: ${debug.original_api_count || 'N/A'}<br>
                                        • Matches after country filtering: ${debug.filtered_count || 'N/A'}<br>
                                        • Excluded matches: ${debug.excluded_count || 0}<br>
                                        • Successfully processed: ${debug.final_processed || processed}<br>
                                        • Processing failures: ${debug.processing_failures || 0}<br><br>

                                        ${debug.excluded_matches && debug.excluded_matches.length > 0 ? `
                                        <strong>Excluded Matches:</strong><br>
                                        ${debug.excluded_matches.slice(0, 5).map(match =>
                                            `• ${match.home_name} vs ${match.away_name} (Competition ID: ${match.competition_id}) - ${match.reason}`
                                        ).join('<br>')}
                                        ${debug.excluded_matches.length > 5 ? `<br>... and ${debug.excluded_matches.length - 5} more` : ''}
                                        ` : ''}
                                    </div>
                                </div>
                            `;
                        }

                        // Show SweetAlert with success message and debug info
                        swal({
                            title: 'Proceso Completado',
                            content: {
                                element: 'div',
                                attributes: {
                                    innerHTML: `
                                        <div style="text-align: center; margin-bottom: 15px;">
                                            <div style="font-size: 16px; color: #28a745; margin-bottom: 10px;">
                                                ✅ ${response.message}
                                            </div>
                                        </div>
                                        ${debugHtml}
                                    `
                                }
                            },
                            icon: 'success',
                            buttons: {
                                confirm: {
                                    text: 'Ok',
                                    value: true,
                                    visible: true,
                                    className: 'btn btn-success',
                                    closeModal: true
                                }
                            }
                        }).then(() => {
                            // Refresh the tables after closing the modal
                            updateTableTorneos();
                            updateTablePartidos();
                        });
                    }, 1000);
                } else {
                    // Continue with next batch
                    setTimeout(function() {
                        processApiBatch(fechaApi, currentBatch + 1, processed);
                    }, 500); // Small delay to show progress
                }
            } else {
                // Handle error
                $('#api-loading-overlay').remove();
                $('button').prop('disabled', false);

                // Show SweetAlert error message
                swal({
                    title: 'Error en el Proceso',
                    text: response.message || 'Error desconocido al cargar partidos via API.',
                    icon: 'error',
                    buttons: {
                        confirm: {
                            text: 'Ok',
                            value: true,
                            visible: true,
                            className: 'btn btn-danger',
                            closeModal: true
                        }
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            // Handle AJAX error
            $('#api-loading-overlay').remove();
            $('button').prop('disabled', false);
            console.error('AJAX Error:', error);
            console.error('Response:', xhr.responseText);

            // Show SweetAlert error message
            swal({
                title: 'Error de Conexión',
                text: 'Error de conexión al cargar partidos via API. Por favor, intente nuevamente.',
                icon: 'error',
                buttons: {
                    confirm: {
                        text: 'Ok',
                        value: true,
                        visible: true,
                        className: 'btn btn-danger',
                        closeModal: true
                    }
                }
            });
        },
        timeout: 60000 // 1 minute timeout per batch
    });
}
</script>
<?php #endregion JS cargar API con loading ?>

<?php #endregion JS ?>

</body>
</html>