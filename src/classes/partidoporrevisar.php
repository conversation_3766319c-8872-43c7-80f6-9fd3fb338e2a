<?php

require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/paisoculto.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';

class PartidoPorRevisar
{
    public string $id;
    public string $matchup;
    public string $pais;
    public string $fecha;
    public ?string $fechadiashora;
    public string $hora;
    public string $horamilitar; 
    public string $diasemana;
    public int $is_done;
    public int $hasexcel;
    public float $formhome;
    public float $formaway;
    public float $home_xg;
    public float $away_xg;
    public ?string $equipo_home;
    public ?string $equipo_away;
    public int $estado;

    #region region no-bd fields
    public string $horastring;
    public string $horamilitartext;
    public int $paiscreado;
    public int $infcargada;
    public string $paiscreadotext;
    public string $infcargadatext;
    public string $paiscreadobgcolor;
    public string $infcargadabgcolor;
    public int $count_all;
    #endregion no-bd fields
    #region region bd variables
    private string $bd_table       = 'partidos_porrevisar';
    private string $bd_alias       = 'parpor';
    private string $bd_id          = 'id_partido_porrevisar';
    private string $bd_matchup     = 'matchup';
    private string $bd_pais        = 'pais';
    private string $bd_fecha       = 'fecha';
    private string $bd_hora        = 'hora';
    private string $bd_horamilitar = 'hora_militar';
    private string $bd_diasemana   = 'dia_semana';
    private string $bd_is_done     = 'is_done';
    private string $bd_hasexcel    = 'has_excel';
    private string $bd_formhome    = 'form_home';
    private string $bd_formaway    = 'form_away';
    private string $bd_home_xg     = 'home_xg';
    private string $bd_away_xg     = 'away_xg';
    private string $bd_equipo_home = 'equipo_home';
    private string $bd_equipo_away = 'equipo_away';
    private string $bd_estado      = 'estado';
    #endregion bd variables

    function __construct()
    {
        $this->id                = '';
        $this->matchup           = '';
        $this->pais              = '';
        $this->fecha             = '';
        $this->hora              = '';
        $this->horastring        = '';
        $this->horamilitar       = '';
        $this->diasemana         = '';
        $this->is_done           = 0;
        $this->hasexcel          = 0;
        $this->formhome          = 0;
        $this->formaway          = 0;
        $this->home_xg           = 0;
        $this->away_xg           = 0;
        $this->equipo_home       = '';
        $this->equipo_away       = '';
        $this->estado            = 0;
        $this->horamilitartext   = '';
        $this->paiscreado        = 0;
        $this->infcargada        = 0;
        $this->paiscreadotext    = '';
        $this->infcargadatext    = '';
        $this->paiscreadobgcolor = '';
        $this->infcargadabgcolor = '';
        $this->count_all         = 0;
    }

    /**
     * @param $resultado
     * @param $conexion
     * @return self
     * @throws Exception
     */
    public static function construct($resultado, $conexion): self
    {
        try {
            $cq = new self;

            $objeto                    = new self;
            $objeto->id                = desordena($resultado[$cq->bd_id]);
            $objeto->matchup           = ($resultado[$cq->bd_matchup]);
            $objeto->pais              = $resultado[$cq->bd_pais];
            $objeto->fecha             = $resultado[$cq->bd_fecha];
            $objeto->hora              = $resultado[$cq->bd_hora];
            $objeto->horamilitar       = $resultado[$cq->bd_horamilitar];
            $objeto->horastring        = convertHoraMilitarAHoraMinutoString($objeto->hora, $objeto->horamilitar);
            $objeto->horamilitartext   = "A las y " . formatDateMinutes($objeto->horamilitar);
            $objeto->fechadiashora     = Partido::formatFechaDiasHora($objeto->fecha, $objeto->horastring);
            $objeto->diasemana         = $resultado[$cq->bd_diasemana];
            $objeto->is_done           = $resultado[$cq->bd_is_done];
            $objeto->hasexcel          = $resultado[$cq->bd_hasexcel];
            $objeto->formhome          = $resultado[$cq->bd_formhome];
            $objeto->formaway          = $resultado[$cq->bd_formaway];
            $objeto->home_xg           = $resultado[$cq->bd_home_xg];
            $objeto->away_xg           = $resultado[$cq->bd_away_xg];
            $objeto->equipo_home       = $resultado[$cq->bd_equipo_home];
            $objeto->equipo_away       = $resultado[$cq->bd_equipo_away];
            $objeto->estado            = $resultado[$cq->bd_estado];
            $objeto->paiscreado        = (!empty(Pais::getByNombre($objeto->pais, $conexion))) ? 1 : 0;
            $objeto->infcargada        = (PartidoInfo::getListCountByPais($objeto->pais, $conexion) > 0) ? 1 : 0;
            $objeto->paiscreadotext    = ($objeto->paiscreado == 1) ? "SI" : "NO";
            $objeto->infcargadatext    = ($objeto->infcargada == 1) ? "SI" : "NO";
            $objeto->paiscreadobgcolor = ($objeto->paiscreado == 1) ? COLOR_SUCCESS : "";
            $objeto->infcargadabgcolor = ($objeto->infcargada == 1) ? COLOR_SUCCESS : "";
            $objeto->count_all         = (isset($resultado['count_all'])) ? $resultado['count_all'] : 0;

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado, $conexion);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getIdProximoPorRevisar($conexion): array
    {
        try {
            $fechahora = create_date() . ' ' . createDateMilitaryHour();

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
            $query .= "  AND $cqa.$cq->bd_is_done = :$cq->bd_is_done ";
            $query .= "  AND CONCAT($cqa.$cq->bd_fecha, ' ', $cqa.$cq->bd_horamilitar) >= :fechahora ";
            $query .= "ORDER BY ";
            $query .= "  $cqa.$cq->bd_fecha ASC ";
            $query .= "  ,$cqa.$cq->bd_hora ASC ";
            $query .= "  ,$cqa.$cq->bd_horamilitar ASC ";
            $query .= "  ,$cqa.$cq->bd_pais DESC ";
            $statement = $conexion->prepare($query);

            //BEGIN bindvalue
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->bindValue(":$cq->bd_is_done", 0);
            $statement->bindValue(":fechahora", $fechahora);
            //END bindvalue

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                $respuesta = array();
                $respuesta['count'] = 0;
                $respuesta['id'] = '';
                
                return $respuesta;
            } else {
                $id = '';
                $n = 0;
                $found = 0;

                foreach ($resultados as $resultado) {
                    if(count(PaisOculto::getList($resultado[$cq->bd_pais], $conexion)) == 0){
                        if($found == 0){
                            $id = $resultado[$cq->bd_id];                        
                            $found = 1;
                        }
                        
                        $n++; 
                    }
                }
                
                $respuesta = array();
                $respuesta['count'] = $n;
                $respuesta['id'] = desordena($id);

                return $respuesta;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getList($paramref, $conexion): array
    {
        try {
            $solotoplay = $paramref['solotoplay'];
            $nom_pais = (isset($paramref['nom_pais'])) ? $paramref['nom_pais'] : "";
            $solo_notdone = (isset($paramref['solo_notdone'])) ? $paramref['solo_notdone'] : 0;
            $grouped_by_torneo = (isset($paramref['grouped_by_torneo'])) ? $paramref['grouped_by_torneo'] : 0;

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";

            if($grouped_by_torneo == 1){
                $query .= ", IFNULL(COUNT($cqa.$cq->bd_id), 0) count_all ";
            }

            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
            
            if($solotoplay == 1){
                $query .= "AND CONCAT($cqa.$cq->bd_fecha, ' ', $cqa.$cq->bd_horamilitar) >= :fechahora ";
            }
            if(!empty($nom_pais)){
                $query .= "AND $cqa.$cq->bd_pais = :$cq->bd_pais ";
            }
            if($solo_notdone == 1){
                $query .= "AND $cqa.$cq->bd_is_done = 0 ";
            }

            if($grouped_by_torneo == 1){
                $query .= "GROUP BY ";
                $query .= "  $cqa.$cq->bd_pais ";
                $query .= "ORDER BY ";
                $query .= "  $cqa.$cq->bd_pais ASC ";
            } else{
                $query .= "ORDER BY ";
                $query .= "  $cqa.$cq->bd_fecha ASC ";
                $query .= "  ,$cqa.$cq->bd_hora ASC ";
                $query .= "  ,$cqa.$cq->bd_horamilitar ASC ";
                $query .= "  ,$cqa.$cq->bd_pais DESC ";
            }

            $statement = $conexion->prepare($query);

            //BEGIN bindvalue
            $statement->bindValue(":$cq->bd_estado", 1);

            if($solotoplay == 1){
                $fechahora = create_date() . ' ' . createDateMilitaryHour();

                $statement->bindValue(":fechahora", $fechahora);
            }
            if(!empty($nom_pais)){
                $statement->bindValue(":$cq->bd_pais", $nom_pais);
            }
            //END bindvalue

            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $partidoporrevisar = self::construct($resultado, $conexion);

                    if(count(PaisOculto::getList($partidoporrevisar->pais, $conexion)) == 0){
                        $listado[] = $partidoporrevisar;
                    }
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $this->validateData();			

            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_matchup ";
            $query .= "  ,$cq->bd_pais ";
            $query .= "  ,$cq->bd_fecha ";
            $query .= "  ,$cq->bd_hora ";
            $query .= "  ,$cq->bd_horamilitar ";
            $query .= "  ,$cq->bd_diasemana ";
            $query .= "  ,$cq->bd_formhome ";
            $query .= "  ,$cq->bd_formaway ";
            $query .= "  ,$cq->bd_home_xg ";
            $query .= "  ,$cq->bd_away_xg ";
            $query .= "  ,$cq->bd_equipo_home ";
            $query .= "  ,$cq->bd_equipo_away ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_matchup ";
            $query .= "  ,:$cq->bd_pais ";
            $query .= "  ,:$cq->bd_fecha ";
            $query .= "  ,:$cq->bd_hora ";
            $query .= "  ,:$cq->bd_horamilitar ";
            $query .= "  ,:$cq->bd_diasemana ";
            $query .= "  ,:$cq->bd_formhome ";
            $query .= "  ,:$cq->bd_formaway ";
            $query .= "  ,:$cq->bd_home_xg ";
            $query .= "  ,:$cq->bd_away_xg ";
            $query .= "  ,:$cq->bd_equipo_home ";
            $query .= "  ,:$cq->bd_equipo_away ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_matchup", $this->matchup);
            $statement->bindValue(":$cq->bd_pais", $this->pais);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_hora", $this->hora);
            $statement->bindValue(":$cq->bd_horamilitar", $this->horamilitar);
            $statement->bindValue(":$cq->bd_diasemana", $this->diasemana);
            $statement->bindValue(":$cq->bd_formhome", $this->formhome);
            $statement->bindValue(":$cq->bd_formaway", $this->formaway);
            $statement->bindValue(":$cq->bd_home_xg", $this->home_xg);
            $statement->bindValue(":$cq->bd_away_xg", $this->away_xg);
            $statement->bindValue(":$cq->bd_equipo_home", $this->equipo_home);
            $statement->bindValue(":$cq->bd_equipo_away", $this->equipo_away);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function uploadCSV($archivocsv, $conexion): void
    {
        try {
            $n = 0;

            foreach ($archivocsv as $filacsv){
                $filacsv = trim($filacsv);
                $separador = ',';

                if ($n > 0) {
                    $campos = explode($separador, $filacsv);

                    $campofecha  = explode(" - ", trim($campos[1]));
                    $fechahora   = convertToStandardDate(trim($campofecha[0])) . ' ' . convert12HourTimeTo24HourTime(trim($campofecha[1]));
                    $fechahora   = convertDateGMTToCOL($fechahora);
                    $fecha       = convertToStandardDate($fechahora);
                    $hora_24time = convert12HourTimeTo24HourTime($fechahora);
                    $horamilitar = $hora_24time;
                    $hora_split  = explode(":", $hora_24time);
                    $hora        = $hora_split[0];
                    $torneo      = trim($campos[3]);
                    $torneo      = str_replace("'", "", $torneo);
                    $pais        = (trim($campos[2]) . ' - ' . $torneo);
                    $home        = (trim($campos[4]));
                    $away        = (trim($campos[5]));
                    $home        = str_replace("'", "", $home);
                    $away        = str_replace("'", "", $away);
                    $formhome    = trim($campos[6]);
                    $formaway    = trim($campos[7]);
                    $home_xg     = $campos[54];
                    $away_xg     = $campos[55];

                    $newpartidoporrevisar              = new self;
                    $newpartidoporrevisar->matchup     = $home . ' -VS- ' . $away;
                    $newpartidoporrevisar->equipo_home = $home;
                    $newpartidoporrevisar->equipo_away = $away;
                    $newpartidoporrevisar->pais        = $pais;
                    $newpartidoporrevisar->fecha       = $fecha;
                    $newpartidoporrevisar->hora        = $hora;
                    $newpartidoporrevisar->horamilitar = $horamilitar;
                    $newpartidoporrevisar->diasemana   = '';
                    $newpartidoporrevisar->formhome    = $formhome;
                    $newpartidoporrevisar->formaway    = $formaway;
                    $newpartidoporrevisar->home_xg     = $home_xg;
                    $newpartidoporrevisar->away_xg     = $away_xg;
                    $newpartidoporrevisar->add($conexion);
                }

                $n++;
            }
    
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modify($conexion): void
    {
        try {
            $this->validateData();

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_matchup = :$cq->bd_matchup  ";
            $query .= "  ,$cq->bd_pais = :$cq->bd_pais  ";
            $query .= "  ,$cq->bd_fecha = :$cq->bd_fecha  ";
            $query .= "  ,$cq->bd_hora = :$cq->bd_hora  ";
            $query .= "  ,$cq->bd_diasemana = :$cq->bd_diasemana  ";
            $query .= "  ,$cq->bd_hasexcel = :$cq->bd_hasexcel  ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_matchup", $this->matchup);
            $statement->bindValue(":$cq->bd_pais", $this->pais);
            $statement->bindValue(":$cq->bd_fecha", $this->fecha);
            $statement->bindValue(":$cq->bd_hora", $this->hora);
            $statement->bindValue(":$cq->bd_diasemana", $this->diasemana);
            $statement->bindValue(":$cq->bd_hasexcel", $this->hasexcel);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modifyDone($conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_is_done = :$cq->bd_is_done  ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_is_done", 1);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function modifyHasExcel($idpartidoporrevisar, $conexion): void
    {
        try {
            $act_partidorevisar = self::get($idpartidoporrevisar, $conexion);

            if($act_partidorevisar->hasexcel == 0){
                $hasexcel = 1;
            } else{
                $hasexcel = 0;
            }

            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_hasexcel = :$cq->bd_hasexcel  ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_hasexcel", $hasexcel);
            $statement->bindValue(":$cq->bd_id", ordena($idpartidoporrevisar));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete_hasta_hora($hora, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = 0 ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_fecha = :$cq->bd_fecha ";
            $query .= "  AND $cq->bd_hora < :$cq->bd_hora ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_fecha", create_date());
            $statement->bindValue(":$cq->bd_hora", $hora);
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete_all_same_torneo($torneo, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_pais = :$cq->bd_pais ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_pais", $torneo);
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function deleteAll($conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_estado = 1 ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    /**
     * @throws Exception
     */
    public static function hasActiveRecordsForDate($fecha, $conexion): bool
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT COUNT(*) as count ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cqa.$cq->bd_estado = :$cq->bd_estado ";
            $query .= "  AND $cqa.$cq->bd_fecha = :$cq->bd_fecha ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->bindValue(":$cq->bd_fecha", $fecha);
            $statement->execute();
            $resultado = $statement->fetch();

            return $resultado['count'] > 0;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function loadFromFootyApi($fecha, $conexion): int
    {
        try {
            // Validate that no active records exist for the selected date
            if (self::hasActiveRecordsForDate($fecha, $conexion)) {
                throw new Exception('Ya existen partidos por revisar en la fecha seleccionada');
            }

            // Build API URL
            $apiKey = FTS_API;
            $apiUrl = "https://api.football-data-api.com/todays-matches?key={$apiKey}&timezone=America/Bogota&date={$fecha}";

            // Make API call
            $response = file_get_contents($apiUrl);
            if ($response === false) {
                throw new Exception('Error al conectar con la API');
            }

            $data = json_decode($response, true);
            if (!$data || !isset($data['data'])) {
                throw new Exception('Respuesta inválida de la API');
            }

            $recordsCreated = 0;
            $timezone = new DateTimeZone('America/Bogota');

            foreach ($data['data'] as $match) {
                try {
                    // Get country name by competition_id
                    $countryName = self::getCountryNameByCompetitionId($match['competition_id'], $conexion);
                    if (empty($countryName)) {
                        continue; // Skip matches without country mapping
                    }

                    // Convert Unix timestamp to Bogotá timezone
                    $dateTime = new DateTime('@' . $match['date_unix']);
                    $dateTime->setTimezone($timezone);

                    // Create new PartidoPorRevisar record
                    $newPartido = new self;
                    $newPartido->matchup = $match['home_name'] . ' -VS- ' . $match['away_name'];
                    $newPartido->equipo_home = $match['home_name'];
                    $newPartido->equipo_away = $match['away_name'];
                    $newPartido->pais = $countryName;
                    $newPartido->fecha = $dateTime->format('Y-m-d');
                    $newPartido->hora = $dateTime->format('G'); // Military hour without minutes
                    $newPartido->horamilitar = $dateTime->format('H:i'); // Full military time
                    $newPartido->diasemana = '';
                    $newPartido->formhome = 0;
                    $newPartido->formaway = 0;
                    $newPartido->home_xg = 0;
                    $newPartido->away_xg = 0;

                    $newPartido->add($conexion);
                    $recordsCreated++;

                } catch (Exception $e) {
                    // Log error but continue processing other matches
                    error_log("Error processing match: " . $e->getMessage());
                    continue;
                }
            }

            return $recordsCreated;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private static function getCountryNameByCompetitionId($competitionId, $conexion): string
    {
        try {
            // Query PaisesTorneoFootyApi to find matching record
            $query = "SELECT ptfa.id_pais ";
            $query .= "FROM paises_torneos_footy_api ptfa ";
            $query .= "WHERE ptfa.id_footy = :competition_id ";
            $query .= "LIMIT 1";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":competition_id", $competitionId);
            $statement->execute();
            $resultado = $statement->fetch();

            if (!$resultado) {
                return ''; // No mapping found
            }

            // Get country name using Pais class
            $pais = Pais::get(desordena($resultado['id_pais']), $conexion);
            return $pais->nombre ?? '';

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function validateData(): void
    {
        try {
            validar_textovacio($this->matchup, 'Debe especificar el matchup');
            validar_textovacio($this->pais, 'Debe especificar el pais');
            validar_textovacio($this->fecha, 'Debe especificar la fecha');
            validar_textovacio($this->hora, 'Debe especificar la hora');

            $this->diasemana = formatDateDayofWeek($this->fecha);

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * Process API matches in batches with progress tracking
     * @param string $fecha Date to process
     * @param int $batch_number Current batch number (0-based)
     * @param PDO $conexion Database connection
     * @return array Progress information
     * @throws Exception
     */
    public static function processApiBatch($fecha, $batch_number, $conexion): array
    {
        try {
            $batch_size = 10; // Process 10 matches per batch
            $session_key = "api_matches_" . $fecha;

            // If this is the first batch (batch_number = 0), fetch data from API
            if ($batch_number === 0) {
                // Validate that no active records exist for the selected date
                if (self::hasActiveRecordsForDate($fecha, $conexion)) {
                    throw new Exception('Ya existen partidos por revisar en la fecha seleccionada');
                }

                // Build API URL and fetch data
                $apiKey = FTS_API;
                $apiUrl = "https://api.football-data-api.com/todays-matches?key={$apiKey}&timezone=America/Bogota&date={$fecha}";

                $response = file_get_contents($apiUrl);
                if ($response === false) {
                    throw new Exception('Error al conectar con la API');
                }

                $data = json_decode($response, true);
                if (!$data || !isset($data['data'])) {
                    throw new Exception('Respuesta inválida de la API');
                }

                // Filter matches that have country mapping
                $validMatches = [];
                foreach ($data['data'] as $match) {
                    $countryName = self::getCountryNameByCompetitionId($match['competition_id'], $conexion);
                    if (!empty($countryName)) {
                        $match['country_name'] = $countryName;
                        $validMatches[] = $match;
                    }
                }

                // Store matches in session for batch processing
                $_SESSION[$session_key] = [
                    'matches' => $validMatches,
                    'total' => count($validMatches),
                    'processed' => 0,
                    'attempted' => 0
                ];

                // If no valid matches, return completed
                if (empty($validMatches)) {
                    unset($_SESSION[$session_key]);
                    return [
                        'processed' => 0,
                        'total' => 0,
                        'completed' => true,
                        'message' => 'No se encontraron partidos válidos para procesar'
                    ];
                }
            }

            // Get matches from session
            if (!isset($_SESSION[$session_key])) {
                throw new Exception('No se encontraron datos de partidos para procesar');
            }

            $sessionData = $_SESSION[$session_key];
            $matches = $sessionData['matches'];
            $total = $sessionData['total'];
            $processed = $sessionData['processed'];

            // Calculate batch range
            $start_index = $batch_number * $batch_size;
            $end_index = min($start_index + $batch_size, $total);

            // Process current batch
            $timezone = new DateTimeZone('America/Bogota');
            $batch_processed = 0;
            $batch_attempted = 0;

            for ($i = $start_index; $i < $end_index; $i++) {
                $batch_attempted++;
                try {
                    $match = $matches[$i];

                    // Convert Unix timestamp to Bogotá timezone
                    $dateTime = new DateTime('@' . $match['date_unix']);
                    $dateTime->setTimezone($timezone);

                    // Create new PartidoPorRevisar record
                    $newPartido = new self;
                    $newPartido->matchup = $match['home_name'] . ' -VS- ' . $match['away_name'];
                    $newPartido->equipo_home = $match['home_name'];
                    $newPartido->equipo_away = $match['away_name'];
                    $newPartido->pais = $match['country_name'];
                    $newPartido->fecha = $dateTime->format('Y-m-d');
                    $newPartido->hora = $dateTime->format('G');
                    $newPartido->horamilitar = $dateTime->format('H:i');
                    $newPartido->diasemana = '';
                    $newPartido->formhome = 0;
                    $newPartido->formaway = 0;
                    $newPartido->home_xg = 0;
                    $newPartido->away_xg = 0;

                    $newPartido->add($conexion);
                    $batch_processed++;

                } catch (Exception $e) {
                    // Log detailed error information for debugging
                    $match_info = isset($match) ? json_encode([
                        'home_name' => $match['home_name'] ?? 'N/A',
                        'away_name' => $match['away_name'] ?? 'N/A',
                        'competition_id' => $match['competition_id'] ?? 'N/A',
                        'date_unix' => $match['date_unix'] ?? 'N/A'
                    ]) : 'Match data not available';

                    error_log("Error processing match " . ($i + 1) . " of {$total}: " . $e->getMessage() . " | Match data: " . $match_info);
                    continue;
                }
            }

            // Update session with new processed count (only successful ones)
            $total_processed = $processed + $batch_processed;
            $_SESSION[$session_key]['processed'] = $total_processed;

            // Update session to track attempted matches to prevent infinite loops
            $total_attempted = ($sessionData['attempted'] ?? 0) + $batch_attempted;
            $_SESSION[$session_key]['attempted'] = $total_attempted;

            // Check if processing is completed - either all processed successfully OR all attempted
            $completed = ($total_processed >= $total) || ($total_attempted >= $total);

            if ($completed) {
                // Clean up session data
                unset($_SESSION[$session_key]);
            }

            $failed_count = $total_attempted - $total_processed;

            return [
                'processed' => $total_processed,
                'total' => $total,
                'completed' => $completed,
                'message' => $completed ?
                    ($failed_count > 0 ?
                        "Se han cargado {$total_processed} partidos desde la API para la fecha {$fecha}. {$failed_count} partidos fallaron al procesar." :
                        "Se han cargado {$total_processed} partidos desde la API para la fecha {$fecha}.") :
                    "Procesando partidos... {$total_processed} de {$total}"
            ];

        } catch (Exception $e) {
            // Clean up session data on error
            if (isset($_SESSION[$session_key])) {
                unset($_SESSION[$session_key]);
            }
            throw new Exception($e->getMessage());
        }
    }
}

?>